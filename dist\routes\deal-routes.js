"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const deal_service_1 = __importDefault(require("../services/deal-service"));
const response_config_1 = __importDefault(require("../config/response-config"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const router = express_1.default.Router();
// 配置multer用于图片上传
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path_1.default.join(__dirname, '../../uploads/deals');
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, `deal-${uniqueSuffix}${path_1.default.extname(file.originalname)}`);
    }
});
const upload = (0, multer_1.default)({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB限制
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
        }
    }
});
/**
 * 获取好价列表（支持多种查询方式）
 * GET /api/deals
 * Query参数：
 * - page: 页码
 * - limit: 每页数量
 * - product_id: 产品ID（获取特定产品的好价）
 * - user_id: 用户ID（获取特定用户的好价）
 * - recent: true（获取最近好价）
 * - sort_by: 排序字段
 * - sort_order: 排序方向
 */
router.get("/", (async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const productId = req.query.product_id;
        const userId = req.query.user_id;
        const recent = req.query.recent === 'true';
        const sortBy = req.query.sort_by;
        const sortOrder = req.query.sort_order;
        let result;
        if (recent) {
            // 获取最近好价（包含产品信息）
            result = await deal_service_1.default.getRecentDealsWithProducts(page, limit);
        }
        else if (productId) {
            // 获取特定产品的好价
            result = await deal_service_1.default.getDealsByProduct(productId, page, limit);
        }
        else if (userId) {
            // 获取特定用户的好价
            result = await deal_service_1.default.getDealsByUser(userId, page, limit);
        }
        else {
            // 通用搜索
            result = await deal_service_1.default.searchDeals({
                page,
                limit,
                sort_by: sortBy,
                sort_order: sortOrder
            });
        }
        res.json(response_config_1.default.pageSuccess({
            deals: result.deals,
        }, result.totalCount, result.totalPages, page, "查询成功", 200));
    }
    catch (error) {
        console.error("Get deals error:", error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 根据ID获取单个好价详情
 * GET /api/deals/:id
 */
router.get("/:id", (async (req, res) => {
    try {
        const dealId = req.params.id;
        const deal = await deal_service_1.default.getDealWithProduct(dealId);
        if (!deal) {
            return res.status(404).json(response_config_1.default.error("好价不存在", 404));
        }
        // 增加浏览次数
        await deal_service_1.default.incrementViews(dealId);
        res.json(response_config_1.default.success(deal, "查询成功", 200));
    }
    catch (error) {
        console.error("Get deal by id error:", error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 创建新好价
 * POST /api/deals
 */
router.post("/", upload.single('purchase_screenshot'), (async (req, res) => {
    try {
        const { product_id, user_id, price, original_price, currency, title, description, purchase_link, store_name, deal_type, expires_at } = req.body;
        // 验证必填字段
        if (!product_id || !user_id || !price || !title) {
            return res.status(400).json(response_config_1.default.error("产品ID、用户ID、价格和标题是必填项", 400));
        }
        if (!req.file) {
            return res.status(400).json(response_config_1.default.error("购买截图是必填项", 400));
        }
        // 构建图片URL
        const purchase_screenshot_url = `/uploads/deals/${req.file.filename}`;
        const dealData = {
            product_id,
            user_id,
            price: parseFloat(price),
            original_price: original_price ? parseFloat(original_price) : undefined,
            currency,
            title: title.trim(),
            description: description?.trim(),
            purchase_screenshot_url,
            purchase_link,
            store_name: store_name?.trim(),
            deal_type,
            expires_at: expires_at ? new Date(expires_at) : undefined
        };
        const deal = await deal_service_1.default.createDeal(dealData);
        res.status(201).json(response_config_1.default.success(deal, "好价创建成功", 201));
    }
    catch (error) {
        console.error("Create deal error:", error);
        // 如果创建失败，删除已上传的文件
        if (req.file) {
            const filePath = path_1.default.join(__dirname, '../../uploads/deals', req.file.filename);
            if (fs_1.default.existsSync(filePath)) {
                fs_1.default.unlinkSync(filePath);
            }
        }
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 更新好价信息
 * PUT /api/deals/:id
 */
router.put("/:id", (async (req, res) => {
    try {
        const dealId = req.params.id;
        const updateData = req.body;
        const deal = await deal_service_1.default.updateDeal(dealId, updateData);
        if (!deal) {
            return res.status(404).json(response_config_1.default.error("好价不存在", 404));
        }
        res.json(response_config_1.default.success(deal, "更新成功", 200));
    }
    catch (error) {
        console.error("Update deal error:", error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 删除好价
 * DELETE /api/deals/:id
 */
router.delete("/:id", (async (req, res) => {
    try {
        const dealId = req.params.id;
        const success = await deal_service_1.default.deleteDeal(dealId);
        if (!success) {
            return res.status(404).json(response_config_1.default.error("好价不存在", 404));
        }
        res.json(response_config_1.default.success(null, "删除成功", 200));
    }
    catch (error) {
        console.error("Delete deal error:", error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 点赞/取消点赞好价
 * POST /api/deals/:id/like
 */
router.post("/:id/like", (async (req, res) => {
    try {
        const dealId = req.params.id;
        const { is_liked } = req.body;
        if (typeof is_liked !== 'boolean') {
            return res.status(400).json(response_config_1.default.error("is_liked参数必须是布尔值", 400));
        }
        await deal_service_1.default.toggleLike(dealId, is_liked);
        res.json(response_config_1.default.success(null, is_liked ? "点赞成功" : "取消点赞成功", 200));
    }
    catch (error) {
        console.error("Toggle like error:", error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 上传好价截图
 * POST /api/deals/upload-screenshot
 */
router.post("/upload-screenshot", upload.single('screenshot'), (async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json(response_config_1.default.error("请选择要上传的图片", 400));
        }
        const imageUrl = `/uploads/deals/${req.file.filename}`;
        res.json(response_config_1.default.success({
            url: imageUrl,
            filename: req.file.filename
        }, "图片上传成功", 200));
    }
    catch (error) {
        console.error("Upload screenshot error:", error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
exports.default = router;
//# sourceMappingURL=deal-routes.js.map