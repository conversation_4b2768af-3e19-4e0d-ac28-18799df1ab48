"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const DealSchema = new mongoose_1.Schema({
    _id: { type: String, required: true },
    product_id: { type: String, required: true, index: true },
    user_id: { type: String, required: true, index: true },
    price: { type: Number, required: true, min: 0 },
    original_price: { type: Number, min: 0 },
    currency: { type: String, default: 'CNY' },
    title: { type: String, required: true, maxlength: 200 },
    description: { type: String, maxlength: 1000 },
    purchase_screenshot_url: { type: String, required: true },
    purchase_link: { type: String },
    store_name: { type: String, maxlength: 100 },
    deal_type: {
        type: String,
        enum: ['discount', 'promotion', 'coupon', 'other'],
        default: 'discount'
    },
    status: {
        type: String,
        enum: ['active', 'expired', 'deleted'],
        default: 'active',
        index: true
    },
    likes_count: { type: Number, default: 0, min: 0 },
    views_count: { type: Number, default: 0, min: 0 },
    comments_count: { type: Number, default: 0, min: 0 },
    expires_at: { type: Date },
    created_at: { type: Date, default: Date.now, index: true },
    updated_at: { type: Date, default: Date.now }
});
// 创建复合索引以优化查询性能
DealSchema.index({ product_id: 1, created_at: -1 });
DealSchema.index({ status: 1, created_at: -1 });
DealSchema.index({ user_id: 1, created_at: -1 });
// 更新时间中间件
DealSchema.pre('save', function (next) {
    this.updated_at = new Date();
    next();
});
// 虚拟字段：计算折扣百分比
DealSchema.virtual('discount_percentage').get(function () {
    if (this.original_price && this.original_price > this.price) {
        return Math.round(((this.original_price - this.price) / this.original_price) * 100);
    }
    return 0;
});
// 确保虚拟字段在JSON序列化时包含
DealSchema.set('toJSON', { virtuals: true });
DealSchema.set('toObject', { virtuals: true });
exports.default = mongoose_1.default.model('Deal', DealSchema);
//# sourceMappingURL=deal.js.map