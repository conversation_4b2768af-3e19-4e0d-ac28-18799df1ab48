import { DealData, DealQueryOptions } from "../repository/deal-repo";
export interface CreateDealData {
    product_id: string;
    user_id: string;
    price: number;
    original_price?: number;
    currency?: string;
    title: string;
    description?: string;
    purchase_screenshot_url: string;
    purchase_link?: string;
    store_name?: string;
    deal_type?: 'discount' | 'promotion' | 'coupon' | 'other';
    expires_at?: Date;
}
export interface UpdateDealData {
    price?: number;
    original_price?: number;
    title?: string;
    description?: string;
    purchase_link?: string;
    store_name?: string;
    deal_type?: 'discount' | 'promotion' | 'coupon' | 'other';
    expires_at?: Date;
}
export interface DealWithProduct extends DealData {
    product?: {
        _id: string;
        name: string;
        brand: string;
        image_url?: string;
    };
}
declare class DealService {
    createDeal(dealData: CreateDealData): Promise<DealData>;
    getDealById(dealId: string): Promise<DealData | null>;
    getDealWithProduct(dealId: string): Promise<DealWithProduct | null>;
    getDealsByProduct(productId: string, page?: number, limit?: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    getDealsByUser(userId: string, page?: number, limit?: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    getRecentDeals(page?: number, limit?: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    getRecentDealsWithProducts(page?: number, limit?: number): Promise<{
        deals: DealWithProduct[];
        totalCount: number;
        totalPages: number;
    }>;
    searchDeals(options: DealQueryOptions): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    updateDeal(dealId: string, updateData: UpdateDealData): Promise<DealData | null>;
    deleteDeal(dealId: string): Promise<boolean>;
    incrementViews(dealId: string): Promise<void>;
    toggleLike(dealId: string, isLiked: boolean): Promise<void>;
    expireDeal(dealId: string): Promise<DealData | null>;
    activateDeal(dealId: string): Promise<DealData | null>;
}
declare const _default: DealService;
export default _default;
//# sourceMappingURL=deal-service.d.ts.map