import { Model, FilterQuery } from "mongoose";

export interface IBaseRepo<T> {
  find(query: FilterQuery<T>, skip?: number, limit?: number): Promise<T[]>;
  findOne(query:FilterQuery<T>):Promise<T | null>;
  count(query: FilterQuery<T>): Promise<number>;
  countAll(): Promise<number>;
  create(data: T): Promise<T>;
  update(id: any, data: Partial<T>): Promise<T | null>;
  delete(id: any): Promise<T | null>;
  upsert(data: T, identifierField: string): Promise<T>;
  findByNameFuzzy(
    namePattern: string,
    skip?: number,
    limit?: number
  ): Promise<T[]>;
  countByNameFuzzy(namePattern: string): Promise<number>;
}

export class BaseRepo<T extends Record<string, any>> implements IBaseRepo<T> {
  protected model: Model<T>;
  protected idField: string;

  constructor(model: Model<any>, idField: string = "_id") {
    this.model = model as Model<T>;
    this.idField = idField;
  }

  // Helper method to convert Mongoose document to plain object
  protected docToObject<D>(doc: D): T {
    if (
      doc &&
      typeof doc === "object" &&
      "toObject" in doc &&
      typeof doc.toObject === "function"
    ) {
      return doc.toObject() as T;
    }
    return doc as unknown as T;
  }

  async find(
    query: FilterQuery<T>,
    skip: number = 0,
    limit: number = 0
  ): Promise<T[]> {
    const results = await this.model.find(query).skip(skip).limit(limit);
    return results.map((doc) => this.docToObject(doc));
  }

  async findOne(query: FilterQuery<T>): Promise<T | null> {
    const doc = await this.model.findOne(query);
    return doc ? this.docToObject(doc) : null;
  }

  async count(query: FilterQuery<T>): Promise<number> {
    return await this.model.countDocuments(query);
  }

  async countAll(): Promise<number> {
    return await this.model.countDocuments({});
  }

  async create(data: T): Promise<T> {
    try {
      const instance = new this.model(data);
      await instance.save();
      return this.docToObject(instance);
    } catch (error: any) {
      if (error.code === 11000) {
        const idValue = data[this.idField];
        const existing = await this.model.findOne({
          [this.idField]: idValue,
        } as any);
        return existing ? this.docToObject(existing) : data;
      }
      throw error;
    }
  }

  async update(id: any, data: Partial<T>): Promise<T | null> {
    const updated = await this.model.findOneAndUpdate(
      { [this.idField]: id } as any,
      data as any,
      { new: true, runValidators: true }
    );
    return updated ? this.docToObject(updated) : null;
  }

  async delete(id: any): Promise<T | null> {
    const deleted = await this.model.findOneAndDelete({
      [this.idField]: id,
    } as any);
    return deleted ? this.docToObject(deleted) : null;
  }

  async upsert(data: T, identifierField: string = this.idField): Promise<T> {
    const idValue = data[identifierField];

    const updated = await this.model.findOneAndUpdate(
      { [identifierField]: idValue } as any,
      data as any,
      { new: true, upsert: true, runValidators: true }
    );
    return updated ? this.docToObject(updated) : data;
  }

  async findByNameFuzzy(
    namePattern: string,
    skip: number = 0,
    limit: number = 0
  ): Promise<T[]> {
    const regex = new RegExp(namePattern, "i");
    const results = await this.model
      .find({ name: { $regex: regex } } as any)
      .skip(skip)
      .limit(limit);
    return results.map((doc) => this.docToObject(doc));
  }

  async countByNameFuzzy(namePattern: string): Promise<number> {
    const regex = new RegExp(namePattern, "i");
    return await this.model.countDocuments({ name: { $regex: regex } });
  }
}

export default BaseRepo;
