import express, { Request, Response, Router, RequestHandler } from 'express';
import brandReviewService from '../services/brand-review-service';
import brandReviewSummaryService from '../services/brand-review-summary-service';
import brandService from '../services/brand-service';
import productService from '../services/product-service';
import productReviewSummaryService from '../services/product-review-summary-service';
import ResponseData from '../config/response-config';
import { auth } from '../middleware/auth';
import { AuthenticatedRequest } from '../types/auth-types';


const router: Router = express.Router();


/**
 * Get daily random brands for homepage
 * This returns 9 random brands that change daily
 */
router.get('/daily-random-brands', (async (req: Request, res: Response) => {
  try {
    const count = parseInt(req.query.count as string) || 9;
    
    // Get today's date in YYYY-MM-DD format to use as seed
    const today = new Date();
    const dateString = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;
    console.log(`Using seed date: ${dateString} for daily random brands`);
    
    // Get all brands
    const allBrands = await brandService.getBrandNamesList();
    if (!allBrands || allBrands.length === 0) {
      return res.json(ResponseData.success(
        [], 
        'No brands found in database', 
        200
      ));
    }
    
    // Create a seeded random function using the date
    const seededRandom = (max: number) => {
      const seed = dateString.split('').reduce((a, b) => {
        return a + b.charCodeAt(0);
      }, 0);
      const x = Math.sin(seed + 1) * 10000;
      return Math.floor((x - Math.floor(x)) * max);
    };
    
    // Shuffle array using Fisher-Yates algorithm with seeded random
    const shuffleArray = (array: any[]) => {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = seededRandom(i + 1);
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    };
    
    // Get shuffled brands and take the first 'count' items
    const shuffledBrands = shuffleArray(allBrands);
    const dailyBrands = shuffledBrands.slice(0, count);
    
    // For each brand, fetch review summary and brand details
    const brandsWithRatings = await Promise.all(
      dailyBrands.map(async (brand) => {
        // Get review summary for this brand
        const summary = await brandReviewSummaryService.getReviewSummaryByBrandId(brand.id);
        
        // Get full brand details to access logo and description
        const brandDetails = await brandService.getBrandById(brand.id);
        
        return {
          brand_id: brand.id,
          name: brand.name,
          logo: brandDetails ? brandDetails.logo_url || '' : '',
          desc: brandDetails ? brandDetails.desc || '' : '',
          // If summary exists and has reviews, show average_rating, otherwise show "暂无评分"
          average_rating: (summary && summary.review_count > 0) ? summary.average_rating : "暂无评分",
          review_count: (summary && summary.review_count > 0) ? summary.review_count : 0
        };
      })
    );
    
    res.json(ResponseData.success(
      brandsWithRatings, 
      'Daily random brands retrieved successfully', 
      200
    ));
  } catch (error) {
    console.error(`Error retrieving daily random brands: ${error.message}`);
    res.status(500).json(ResponseData.error(`Failed to retrieve daily random brands: ${error.message}`, 500));
  }
}) as RequestHandler);

/**
 * Get popular brands for homepage
 */
router.get('/popular-brands', (async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const ratingWeight = parseFloat(req.query.ratingWeight as string) || 0.5;
    
    // Get popular brands from service
    const popularBrandSummaries = await brandReviewSummaryService.getPopularBrands(limit, ratingWeight);
    
    // Fetch additional brand details needed for display
    const popularBrands = await Promise.all(
      popularBrandSummaries.map(async (summary) => {
        const brand = await brandService.getBrandById(summary.brand_id); // Changed from product_id to brand_id
        
        // Return only the data needed for homepage display
        return {
          brand_id: summary.brand_id, // Changed from product_id to brand_id
          name: brand ? brand.name : 'Unknown Brand',
          average_rating: summary.average_rating,
          review_count: summary.review_count
        };
      })
    );
    
    res.json(ResponseData.success(
      popularBrands, 
      'Popular brands retrieved successfully', 
      200
    ));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get all brands with reviews (paginated)
 * This API is used for brand review summary management
 */
router.get('/brands-with-reviews', (async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const sortBy = req.query.sortBy as string || 'review_count'; // Default sort by review count
    const sortOrder = req.query.sortOrder as string || 'desc'; // Default sort order is descending
    
    // Validate sort parameters
    const validSortFields = ['average_rating', 'review_count', 'created_at', 'updated_at'];
    const validSortOrders = ['asc', 'desc'];
    
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json(
        ResponseData.error(`Invalid sort field. Valid options are: ${validSortFields.join(', ')}`, 400)
      );
    }
    
    if (!validSortOrders.includes(sortOrder)) {
      return res.status(400).json(
        ResponseData.error("Invalid sort order. Use 'asc' or 'desc'", 400)
      );
    }
    
    // Get brands with reviews
    const result = await brandReviewSummaryService.getBrandsWithReviews(page, limit, sortBy, sortOrder);
    
    // For each brand summary, fetch the actual brand name
    const brandsWithDetails = await Promise.all(
      result.summaries.map(async (summary) => {
        const brand = await brandService.getBrandById(summary.brand_id); // Changed from product_id to brand_id
        return {
          ...summary,
          brand_name: brand ? brand.name : 'Unknown Brand'
        };
      })
    );
    
    res.json(ResponseData.pageSuccess({
      brands: brandsWithDetails,
      sortBy,
      sortOrder
    }, result.totalCount, result.totalPages, page, 'Brands with reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get review summary for a brand
 */
router.get('/summary/:brandId', (async (req: Request, res: Response) => {
  try {
    const { brandId } = req.params;
    
    // Check if brand exists
    const brand = await brandService.getBrandById(brandId);
    if (!brand) {
      return res.status(404).json(ResponseData.error('Brand not found', 404));
    }
    
    // Get or initialize review summary
    let summary = await brandReviewSummaryService.getReviewSummaryByBrandId(brandId);
    if (!summary) {
      summary = await brandReviewSummaryService.createInitialReviewSummary(brandId);
    }
    
    res.json(ResponseData.success(summary, 'Review summary retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get all reviews for a brand with pagination
 */
router.get('/brand/:brandId', (async (req: Request, res: Response) => {
  try {
    const { brandId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const type = req.query.type as string;
    const sortBy = req.query.sortBy as string || 'latest'; // 'latest' or 'hot'
    const includeReplies = req.query.includeReplies !== 'false';
    const maxReplyDepth = parseInt(req.query.maxReplyDepth as string) || 3;
    
    let result;
    
    switch (type) {
      case 'long':
        result = await brandReviewService.getLongReviews(brandId, page, limit, includeReplies, maxReplyDepth, sortBy);
        break;
      case 'short':
        result = await brandReviewService.getShortReviews(brandId, page, limit, includeReplies, maxReplyDepth, sortBy);
        break;
      default:
        result = await brandReviewService.getReviewsByBrandId(brandId, page, limit, includeReplies, maxReplyDepth, sortBy);
    }
    
    res.json(ResponseData.pageSuccess({
      reviews: result.reviews,
      includeReplies,
      maxReplyDepth,
      sortBy
    }, result.totalCount, result.totalPages, page, 'Reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get recommended reviews for a brand
 */
router.get('/recommended/:brandId', (async (req: Request, res: Response) => {
  try {
    const { brandId } = req.params;
    const limit = parseInt(req.query.limit as string) || 5;
    
    const reviews = await brandReviewService.getRecommendedReviews(brandId, limit);
    
    res.json(ResponseData.success(reviews, 'Recommended reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get replies to a specific review
 */
router.get('/:reviewId/replies', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    // Add support for controlling max reply depth
    const maxReplyDepth = parseInt(req.query.maxReplyDepth as string) || 3;
    
    const result = await brandReviewService.getReviewReplies(reviewId, page, limit, maxReplyDepth);
    
    res.json(ResponseData.pageSuccess({
      replies: result.replies,
      maxReplyDepth
    }, result.totalCount, result.totalPages, page, 'Review replies retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get a specific review by ID
 */
router.get('/:reviewId', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    console.log(`API: Fetching brand review with ID: ${reviewId}`);
    
    // First try to find by review_id (business ID)
    let review = await brandReviewService.findReviewByReviewId(reviewId);
    
    // If not found, try by MongoDB _id
    if (!review) {
      review = await brandReviewService.findReviewById(reviewId);
    }
    
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }
    
    res.json(ResponseData.success(review, 'Review retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Create a new review or reply
 */
router.post('/', (async (req: Request, res: Response) => {
  try {
    const { 
      brand_id, user_id, content, rating, parent_review_id, 
      title, images, is_long_review 
    } = req.body;
    
    // Validation
    if (!brand_id) {
      return res.status(400).json(ResponseData.error('Brand ID is required', 400));
    }
    
    if (!user_id) {
      return res.status(400).json(ResponseData.error('User ID is required', 400));
    }
    
    if (!content || content.trim() === '') {
      return res.status(400).json(ResponseData.error('Review content is required', 400));
    }
    
    if (!parent_review_id && (!rating || rating < 1 || rating > 10)) {
      return res.status(400).json(ResponseData.error('Rating must be between 1 and 10', 400));
    }
    
    // Content length validation
    if (is_long_review && content.length > 3000) {
      return res.status(400).json(ResponseData.error('Long review content cannot exceed 3000 characters', 400));
    }
    
    if (!is_long_review && content.length > 50) {
      return res.status(400).json(ResponseData.error('Short review content cannot exceed 50 characters', 400));
    }
    
    // Title validation for long reviews
    if (is_long_review && title && (title.length < 3 || title.length > 20)) {
      return res.status(400).json(ResponseData.error('Title must be between 3 and 20 characters for long reviews', 400));
    }
    
    // Prevent title for short reviews
    if (!is_long_review && title) {
      return res.status(400).json(ResponseData.error('Short reviews cannot have a title', 400));
    }
    
    // Prevent images for short reviews
    if (!is_long_review && images && images.length > 0) {
      return res.status(400).json(ResponseData.error('Short reviews cannot have images', 400));
    }
    
    const review = await brandReviewService.createReview({
      brand_id,
      user_id,
      content,
      rating: parent_review_id ? 1 : rating, // Replies need rating >= 1
      parent_review_id,
      title,
      images,
      is_long_review: parent_review_id ? false : is_long_review, // Replies are always short
      is_recommended: false,
      review_status: true,
      liked_by: [], // Add empty liked_by array
      disliked_by: [] // Add empty disliked_by array
    });
    
    res.status(201).json(ResponseData.success(review, 'Review created successfully', 201));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Update an existing review
 */
router.put('/:reviewId', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    const { content, rating, title, images } = req.body;
    
    // Get existing review
    const existingReview = await brandReviewService.getReviewById(reviewId);
    if (!existingReview) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }
    
    // Content length validation
    if (content) {
      if (existingReview.is_long_review && content.length > 3000) {
        return res.status(400).json(ResponseData.error('Long review content cannot exceed 3000 characters', 400));
      }
      
      if (!existingReview.is_long_review && content.length > 50) {
        return res.status(400).json(ResponseData.error('Short review content cannot exceed 50 characters', 400));
      }
    }
    
    // Title validation
    if (title) {
      if (existingReview.is_long_review && (title.length < 3 || title.length > 20)) {
        return res.status(400).json(ResponseData.error('Title must be between 3 and 20 characters for long reviews', 400));
      }
      
      if (!existingReview.is_long_review) {
        return res.status(400).json(ResponseData.error('Short reviews cannot have a title', 400));
      }
    }
    
    // Images validation
    if (images && !existingReview.is_long_review) {
      return res.status(400).json(ResponseData.error('Short reviews cannot have images', 400));
    }
    
    // Rating validation
    if (rating && (rating < 1 || rating > 10)) {
      return res.status(400).json(ResponseData.error('Rating must be between 1 and 10', 400));
    }
    
    // Update the review
    const updateData: any = {};
    if (content) updateData.content = content;
    if (rating && !existingReview.parent_review_id) updateData.rating = rating;
    if (existingReview.is_long_review && title) updateData.title = title;
    if (existingReview.is_long_review && images) updateData.images = images;
    
    const updatedReview = await brandReviewService.updateReview(reviewId, updateData);
    
    res.json(ResponseData.success(updatedReview, 'Review updated successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Delete a review
 */
router.delete('/:reviewId', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    
    const deletedReview = await brandReviewService.deleteReview(reviewId);
    if (!deletedReview) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }
    
    res.json(ResponseData.success(deletedReview, 'Review deleted successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Like a review
 */
router.post('/:reviewId/like', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await brandReviewService.likeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review liked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Unlike a review
 */
router.post('/:reviewId/unlike', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await brandReviewService.unlikeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review unliked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Dislike a review
 */
router.post('/:reviewId/dislike', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await brandReviewService.dislikeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review disliked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Undislike a review
 */
router.post('/:reviewId/undislike', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await brandReviewService.undislikeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review undisliked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get user reaction status for a review
 */
router.get('/:reviewId/reaction/:userId', (async (req: Request, res: Response) => {
  try {
    const { reviewId, userId } = req.params;

    const reactionStatus = await brandReviewService.getUserReactionStatus(reviewId, userId);

    res.json(ResponseData.success(reactionStatus, 'User reaction status retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Check if user has already reviewed a brand
 */
router.get('/brand/:brandId/user/:userId/has-reviewed', (async (req: Request, res: Response) => {
  try {
    const { brandId, userId } = req.params;

    const hasReviewed = await brandReviewService.hasUserReviewedBrand(brandId, userId);

    res.json(ResponseData.success({ hasReviewed }, 'User review status retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get user's review for a specific brand
 */
router.get('/brand/:brandId/user/:userId/review', (async (req: Request, res: Response) => {
  try {
    const { brandId, userId } = req.params;

    const review = await brandReviewService.getUserReviewForBrand(brandId, userId);

    if (review) {
      res.json(ResponseData.success(review, 'User review retrieved successfully', 200));
    } else {
      res.json(ResponseData.success(null, 'User has not reviewed this brand', 200));
    }
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get all reviews by a specific user
 */
router.get('/user/:userId/reviews', (async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await brandReviewService.getUserReviews(userId, page, limit);

    res.json(ResponseData.pageSuccess({
      reviews: result.reviews
    }, result.totalCount, result.totalPages, page, 'User reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get rating distribution for a brand
 */
router.get('/brand/:brandId/rating-distribution', (async (req: Request, res: Response) => {
  try {
    const { brandId } = req.params;

    const distribution = await brandReviewService.getRatingDistribution(brandId);
    const percentages = await brandReviewService.getRatingDistributionPercentages(brandId);

    res.json(ResponseData.success({
      distribution,
      percentages
    }, 'Rating distribution retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get top-rated brands
 */
router.get('/top-rated/brands', (async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    
    const topRated = await brandReviewSummaryService.getTopRatedBrands(limit);
    
    res.json(ResponseData.success(topRated, 'Top rated brands retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get most-reviewed brands
 */
router.get('/most-reviewed/brands', (async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    
    const mostReviewed = await brandReviewSummaryService.getMostReviewedBrands(limit);
    
    res.json(ResponseData.success(mostReviewed, 'Most reviewed brands retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get top products for a specific brand
 * Returns the most popular products (based on reviews and ratings) for a given brand
 */
router.get('/brand/:brandId/top-products', (async (req: Request, res: Response) => {
  try {
    const { brandId } = req.params;
    const limit = parseInt(req.query.limit as string) || 5; // Default to 5 products
    
    // Check if brand exists
    const brand = await brandService.getBrandById(brandId);
    if (!brand) {
      return res.status(404).json(ResponseData.error('Brand not found', 404));
    }
    
    // Get all products for this brand
    const allBrandProducts = await productService.getProductsByBrand(brand.name);
    if (!allBrandProducts || allBrandProducts.length === 0) {
      return res.json(ResponseData.success(
        [], 
        'No products found for this brand', 
        200
      ));
    }
    
    // Get review summaries for all products
    const productsWithStats = await Promise.all(
      allBrandProducts.map(async (product) => {
        const summary = await productReviewSummaryService.getReviewSummaryByProductId(product._id);
        
        return {
          product_id: product._id,
          name: product.name,
          brand: product.brand,
          image_url: product.image_url || '',
          average_rating: summary ? summary.average_rating : 0,
          review_count: summary ? summary.review_count : 0,
          // Calculate a popularity score (weighted combination of rating and review count)
          popularity_score: (summary ? summary.average_rating * 0.4 : 0) + 
                           (summary ? summary.review_count * 0.6 : 0)
        };
      })
    );
    
    // Sort by popularity score (descending) and take the top N
    const topProducts = productsWithStats
      .sort((a, b) => b.popularity_score - a.popularity_score)
      .slice(0, limit);
    
    res.json(ResponseData.success(
      topProducts, 
      `Top ${limit} products for ${brand.name} retrieved successfully`, 
      200
    ));
  } catch (error) {
    console.error(`Error retrieving top products for brand: ${error.message}`);
    res.status(500).json(ResponseData.error(`Failed to retrieve top products: ${error.message}`, 500));
  }
}) as RequestHandler);

export default router;