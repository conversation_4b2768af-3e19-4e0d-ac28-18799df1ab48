import { IBaseRepo, BaseRepo } from "./base-repo";
export interface DealData {
    _id: string;
    product_id: string;
    user_id: string;
    price: number;
    original_price?: number;
    currency: string;
    title: string;
    description?: string;
    purchase_screenshot_url: string;
    purchase_link?: string;
    store_name?: string;
    deal_type: 'discount' | 'promotion' | 'coupon' | 'other';
    status: 'active' | 'expired' | 'deleted';
    likes_count: number;
    views_count: number;
    comments_count: number;
    expires_at?: Date;
}
export interface DealQueryOptions {
    product_id?: string;
    user_id?: string;
    status?: 'active' | 'expired' | 'deleted';
    deal_type?: 'discount' | 'promotion' | 'coupon' | 'other';
    page?: number;
    limit?: number;
    sort_by?: 'created_at' | 'price' | 'likes_count' | 'views_count';
    sort_order?: 'asc' | 'desc';
}
export interface IDealRepo extends IBaseRepo<DealData> {
    getDealsByProduct(productId: string, page: number, limit: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    getDealsByUser(userId: string, page: number, limit: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    getRecentDeals(page: number, limit: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    searchDeals(options: DealQueryOptions): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    incrementViews(dealId: string): Promise<void>;
    incrementLikes(dealId: string): Promise<void>;
    decrementLikes(dealId: string): Promise<void>;
    updateDealStatus(dealId: string, status: 'active' | 'expired' | 'deleted'): Promise<DealData | null>;
}
export declare class DealRepo extends BaseRepo<DealData> implements IDealRepo {
    constructor();
    getDealsByProduct(productId: string, page?: number, limit?: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    getDealsByUser(userId: string, page?: number, limit?: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    getRecentDeals(page?: number, limit?: number): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    searchDeals(options: DealQueryOptions): Promise<{
        deals: DealData[];
        totalCount: number;
        totalPages: number;
    }>;
    incrementViews(dealId: string): Promise<void>;
    incrementLikes(dealId: string): Promise<void>;
    decrementLikes(dealId: string): Promise<void>;
    updateDealStatus(dealId: string, status: 'active' | 'expired' | 'deleted'): Promise<DealData | null>;
}
declare const _default: DealRepo;
export default _default;
//# sourceMappingURL=deal-repo.d.ts.map