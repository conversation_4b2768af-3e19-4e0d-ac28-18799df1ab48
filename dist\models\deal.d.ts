import mongoose, { Document } from 'mongoose';
export interface IDeal extends Document {
    _id: string;
    product_id: string;
    user_id: string;
    price: number;
    original_price?: number;
    currency: string;
    title: string;
    description?: string;
    purchase_screenshot_url: string;
    purchase_link?: string;
    store_name?: string;
    deal_type: 'discount' | 'promotion' | 'coupon' | 'other';
    status: 'active' | 'expired' | 'deleted';
    likes_count: number;
    views_count: number;
    comments_count: number;
    expires_at?: Date;
    created_at: Date;
    updated_at: Date;
}
declare const _default: mongoose.Model<IDeal, {}, {}, {}, mongoose.Document<unknown, {}, IDeal, {}> & IDeal & Required<{
    _id: string;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=deal.d.ts.map