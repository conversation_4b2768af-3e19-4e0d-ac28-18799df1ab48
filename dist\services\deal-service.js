"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const deal_repo_1 = __importDefault(require("../repository/deal-repo"));
const product_repo_1 = __importDefault(require("../repository/product-repo"));
const id_generator_1 = require("../utils/id-generator");
class DealService {
    async createDeal(dealData) {
        // 验证产品是否存在
        const product = await product_repo_1.default.findById(dealData.product_id);
        if (!product) {
            throw new Error("指定的产品不存在");
        }
        // 验证价格
        if (dealData.price <= 0) {
            throw new Error("价格必须大于0");
        }
        if (dealData.original_price && dealData.original_price <= dealData.price) {
            throw new Error("原价必须大于现价");
        }
        // 生成ID并创建好价
        const dealId = (0, id_generator_1.generateId)();
        const newDeal = {
            _id: dealId,
            product_id: dealData.product_id,
            user_id: dealData.user_id,
            price: dealData.price,
            original_price: dealData.original_price,
            currency: dealData.currency || 'CNY',
            title: dealData.title.trim(),
            description: dealData.description?.trim(),
            purchase_screenshot_url: dealData.purchase_screenshot_url,
            purchase_link: dealData.purchase_link,
            store_name: dealData.store_name?.trim(),
            deal_type: dealData.deal_type || 'discount',
            status: 'active',
            likes_count: 0,
            views_count: 0,
            comments_count: 0,
            expires_at: dealData.expires_at
        };
        return await deal_repo_1.default.create(newDeal);
    }
    async getDealById(dealId) {
        return await deal_repo_1.default.findById(dealId);
    }
    async getDealWithProduct(dealId) {
        const deal = await deal_repo_1.default.findById(dealId);
        if (!deal)
            return null;
        const product = await product_repo_1.default.findById(deal.product_id);
        return {
            ...deal,
            product: product ? {
                _id: product._id,
                name: product.name,
                brand: product.brand,
                image_url: product.image_url
            } : undefined
        };
    }
    async getDealsByProduct(productId, page = 1, limit = 10) {
        return await deal_repo_1.default.getDealsByProduct(productId, page, limit);
    }
    async getDealsByUser(userId, page = 1, limit = 10) {
        return await deal_repo_1.default.getDealsByUser(userId, page, limit);
    }
    async getRecentDeals(page = 1, limit = 10) {
        return await deal_repo_1.default.getRecentDeals(page, limit);
    }
    async getRecentDealsWithProducts(page = 1, limit = 10) {
        const result = await deal_repo_1.default.getRecentDeals(page, limit);
        // 获取所有相关产品信息
        const productIds = [...new Set(result.deals.map(deal => deal.product_id))];
        const products = await Promise.all(productIds.map(id => product_repo_1.default.findById(id)));
        const productMap = new Map();
        products.forEach(product => {
            if (product) {
                productMap.set(product._id, {
                    _id: product._id,
                    name: product.name,
                    brand: product.brand,
                    image_url: product.image_url
                });
            }
        });
        const dealsWithProducts = result.deals.map(deal => ({
            ...deal,
            product: productMap.get(deal.product_id)
        }));
        return {
            deals: dealsWithProducts,
            totalCount: result.totalCount,
            totalPages: result.totalPages
        };
    }
    async searchDeals(options) {
        return await deal_repo_1.default.searchDeals(options);
    }
    async updateDeal(dealId, updateData) {
        const existingDeal = await deal_repo_1.default.findById(dealId);
        if (!existingDeal) {
            throw new Error("好价不存在");
        }
        // 验证价格
        if (updateData.price !== undefined && updateData.price <= 0) {
            throw new Error("价格必须大于0");
        }
        if (updateData.original_price && updateData.price && updateData.original_price <= updateData.price) {
            throw new Error("原价必须大于现价");
        }
        const updatedData = {};
        if (updateData.price !== undefined)
            updatedData.price = updateData.price;
        if (updateData.original_price !== undefined)
            updatedData.original_price = updateData.original_price;
        if (updateData.title !== undefined)
            updatedData.title = updateData.title.trim();
        if (updateData.description !== undefined)
            updatedData.description = updateData.description?.trim();
        if (updateData.purchase_link !== undefined)
            updatedData.purchase_link = updateData.purchase_link;
        if (updateData.store_name !== undefined)
            updatedData.store_name = updateData.store_name?.trim();
        if (updateData.deal_type !== undefined)
            updatedData.deal_type = updateData.deal_type;
        if (updateData.expires_at !== undefined)
            updatedData.expires_at = updateData.expires_at;
        return await deal_repo_1.default.update(dealId, updatedData);
    }
    async deleteDeal(dealId) {
        const deal = await deal_repo_1.default.findById(dealId);
        if (!deal) {
            throw new Error("好价不存在");
        }
        // 软删除：更新状态为deleted
        await deal_repo_1.default.updateDealStatus(dealId, 'deleted');
        return true;
    }
    async incrementViews(dealId) {
        await deal_repo_1.default.incrementViews(dealId);
    }
    async toggleLike(dealId, isLiked) {
        if (isLiked) {
            await deal_repo_1.default.incrementLikes(dealId);
        }
        else {
            await deal_repo_1.default.decrementLikes(dealId);
        }
    }
    async expireDeal(dealId) {
        return await deal_repo_1.default.updateDealStatus(dealId, 'expired');
    }
    async activateDeal(dealId) {
        return await deal_repo_1.default.updateDealStatus(dealId, 'active');
    }
}
exports.default = new DealService();
//# sourceMappingURL=deal-service.js.map