import express, { Request, Response, NextFunction, ErrorRequestHand<PERSON> } from "express";
import cors from "cors";
import path from "path";
import { connectDB } from "./config/db";
import requestLogger from "./middleware/request-logger";
import brandRoutes from "./routes/brand-routes";
import productRoutes from "./routes/product-routes";
import permissionRoutes from "./routes/permission-routes";
import roleRoutes from "./routes/role-routes";
import userRoutes from "./routes/user-routes";
import i18nRoutes from "./routes/i18n-routes";
import authRoutes from "./routes/auth-routes";
import reviewRoutes from "./routes/product-review-routes"; 
import brandReviewRoutes from './routes/brand-review-routes'
import fontUserRoutes from './routes/font-user-routes'
import fontAuthRoutes from './routes/font-auth-routes'
import threadRoutes from './routes/thread-routes'
import messageRoutes from './routes/message-routes'
import searchRoutes from './routes/search-routes'
import userStatsRoutes from './routes/user-stats-routes'
import historyRoutes from './routes/history-routes'


// In CommonJS, __dirname is available globally

const app = express();
const port = process.env.PORT || 3000;

app.use(
  cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// 添加请求日志中间件（在其他中间件之前）
app.use(requestLogger.middleware());

// 允许解析JSON请求体
app.use(express.json());

// Serve static files for uploaded images
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Serve static files from assets folder for product images - 使用相对路径
app.use('/assets', express.static(path.join(__dirname, '../assets')));

connectDB()
  .then(() => console.log("MongoDB 数据库启动成功"))
  .catch((err) => {
    console.error("MongoDB 数据连接失败:", err);
    process.exit(1);
  });

// 主页路由
app.get("/", async (req: Request, res: Response) => {
  res.json({ message: "好宠粮 后端 API 工作正常" });
});

// API路由
app.use("/api/brands", brandRoutes);
app.use("/api/products", productRoutes);
app.use("/api/permissions", permissionRoutes);
app.use("/api/roles", roleRoutes);
app.use("/api/users", userRoutes);
app.use("/api/i18n", i18nRoutes);
app.use("/api/auth", authRoutes);
app.use("/api/reviews", reviewRoutes); 
app.use('/api/brand-reviews', brandReviewRoutes);
app.use('/api/font-user', fontUserRoutes);
app.use('/api/font-auth', fontAuthRoutes);
app.use('/api/threads', threadRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/user-stats', userStatsRoutes);
app.use('/api/history', historyRoutes);

// 添加日志统计端点 - 分别定义两个路由
app.get("/api/logs/stats", async (req: Request, res: Response) => {
  try {
    const stats = await requestLogger.generateStats();
    res.json({ success: true, data: stats });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get("/api/logs/stats/:date", async (req: Request, res: Response) => {
  try {
    const { date } = req.params;
    const stats = await requestLogger.generateStats(date);
    res.json({ success: true, data: stats });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.listen(port, () => {
  console.log(`服务器开启： http://localhost:${port}`);
  console.log(`请求日志将保存到: logs/ 目录`);
  console.log(`查看今日统计: GET /api/logs/stats`);
  console.log(`查看指定日期统计: GET /api/logs/stats/YYYY-MM-DD`);
});
