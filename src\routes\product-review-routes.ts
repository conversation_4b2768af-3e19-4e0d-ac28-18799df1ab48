import express, { Request, Response, Router, RequestHandler } from 'express';
import reviewService from '../services/review-service';
import productReviewSummaryService from '../services/product-review-summary-service';
import productService from '../services/product-service';
import ResponseData from '../config/response-config';
import { ReviewData } from '../repository/review-repo';
import { ProductReviewSummaryData } from '../repository/product-review-summary-repo';
import { auth } from '../middleware/auth';
import { AuthenticatedRequest } from '../types/auth-types';

const router: Router = express.Router();


/**
 * Get daily random products for homepage
 * This returns 9 random products that change daily
 */
router.get('/daily-random-products', (async (req: Request, res: Response) => {
  try {
    const count = parseInt(req.query.count as string) || 9;
    
    // Get today's date in YYYY-MM-DD format to use as seed
    const today = new Date();
    const dateString = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;
    console.log(`Using seed date: ${dateString} for daily random products`);
    
    // Get all products
    const allProducts = await productService.getAllProducts();
    if (!allProducts || allProducts.length === 0) {
      return res.json(ResponseData.success(
        [], 
        'No products found in database', 
        200
      ));
    }
    
    // Create a seeded random function using the date
    const seededRandom = (max: number) => {
      const seed = dateString.split('').reduce((a, b) => {
        return a + b.charCodeAt(0);
      }, 0);
      const x = Math.sin(seed + 1) * 10000;
      return Math.floor((x - Math.floor(x)) * max);
    };
    
    // Shuffle array using Fisher-Yates algorithm with seeded random
    const shuffleArray = (array: any[]) => {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = seededRandom(i + 1);
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    };
    
    // Get shuffled products and take the first 'count' items
    const shuffledProducts = shuffleArray(allProducts);
    const dailyProducts = shuffledProducts.slice(0, count);
    
    // For each product, fetch review summary or mark as "暂无评分" if no reviews
    const productsWithRatings = await Promise.all(
      dailyProducts.map(async (product) => {
        // Get review summary for this product
        const summary = await productReviewSummaryService.getReviewSummaryByProductId(product._id);
        
        return {
          product_id: product._id,
          name: product.name,
          brand: product.brand,
          image_url: product.image_url,
          // If summary exists and has reviews, show average_rating, otherwise show "暂无评分"
          average_rating: (summary && summary.review_count > 0) ? summary.average_rating : "暂无评分",
          review_count: (summary && summary.review_count > 0) ? summary.review_count : 0
        };
      })
    );
    
    res.json(ResponseData.success(
      productsWithRatings, 
      'Daily random products retrieved successfully', 
      200
    ));
  } catch (error) {
    console.error(`Error retrieving daily random products: ${error.message}`);
    res.status(500).json(ResponseData.error(`Failed to retrieve daily random products: ${error.message}`, 500));
  }
}) as RequestHandler);




/**
 * Get popular products for homepage
 * This returns the top 10 products with the most reviews, with a small weight for rating
 */
router.get('/popular-products', (async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const ratingWeight = parseFloat(req.query.ratingWeight as string) || 0.5;
    
    // Get popular products from service
    const popularProductsSummaries = await productReviewSummaryService.getPopularProducts(limit, ratingWeight);
    
    // Fetch additional product details needed for display
    const popularProducts = await Promise.all(
      popularProductsSummaries.map(async (summary) => {
        const product = await productService.getProductById(summary.product_id);
        
        // Return only the data needed for homepage display
        return {
          product_id: summary.product_id,
          name: product ? product.name : 'Unknown Product',
          brand: product ? product.brand : 'Unknown Brand',
          image_url: product ? product.image_url : null,
          average_rating: summary.average_rating,
          review_count: summary.review_count
        };
      })
    );
    
    res.json(ResponseData.success(
      popularProducts, 
      'Popular products retrieved successfully', 
      200
    ));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);





/**
 * Get all products with reviews (paginated)
 * This API is used for product review summary management
 */
router.get('/products-with-reviews', (async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const sortBy = req.query.sortBy as string || 'review_count'; // Default sort by review count
    const sortOrder = req.query.sortOrder as string || 'desc'; // Default sort order is descending
    
    // Validate sort parameters
    const validSortFields = ['average_rating', 'review_count', 'created_at', 'updated_at'];
    const validSortOrders = ['asc', 'desc'];
    
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json(
        ResponseData.error(`Invalid sort field. Valid options are: ${validSortFields.join(', ')}`, 400)
      );
    }
    
    if (!validSortOrders.includes(sortOrder)) {
      return res.status(400).json(
        ResponseData.error("Invalid sort order. Use 'asc' or 'desc'", 400)
      );
    }
    
    // Get products with reviews
    const result = await productReviewSummaryService.getProductsWithReviews(page, limit, sortBy, sortOrder);
    
    // For each product summary, fetch the actual product name
    const productsWithDetails = await Promise.all(
      result.summaries.map(async (summary) => {
        const product = await productService.getProductById(summary.product_id);
        return {
          ...summary,
          product_name: product ? product.name : 'Unknown Product',
          brand: product ? product.brand : 'Unknown Brand'
        };
      })
    );
    
    res.json(ResponseData.pageSuccess({
      products: productsWithDetails,
      sortBy,
      sortOrder
    }, result.totalCount, result.totalPages, page, 'Products with reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);
/**
 * Get review summary for a product
 */
router.get('/summary/:productId', (async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    
    // Check if product exists
    const product = await productService.getProductById(productId);
    if (!product) {
      return res.status(404).json(ResponseData.error('Product not found', 404));
    }
    
    // Get or initialize review summary
    let summary = await productReviewSummaryService.getReviewSummaryByProductId(productId);
    if (!summary) {
      summary = await productReviewSummaryService.createInitialReviewSummary(productId);
    }
    
    res.json(ResponseData.success(summary, 'Review summary retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get all reviews for a product with pagination
 */
router.get('/product/:productId', (async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const type = req.query.type as string;
    const sortBy = req.query.sortBy as string || 'latest'; // 'latest' or 'hot'
    const includeReplies = req.query.includeReplies !== 'false';
    const maxReplyDepth = parseInt(req.query.maxReplyDepth as string) || 3;
    
    let result;
    
    switch (type) {
      case 'long':
        result = await reviewService.getLongReviews(productId, page, limit, includeReplies, maxReplyDepth, sortBy);
        break;
      case 'short':
        result = await reviewService.getShortReviews(productId, page, limit, includeReplies, maxReplyDepth, sortBy);
        break;
      default:
        result = await reviewService.getReviewsByProductId(productId, page, limit, includeReplies, maxReplyDepth, sortBy);
    }
    
    res.json(ResponseData.pageSuccess({
      reviews: result.reviews,
      includeReplies,
      maxReplyDepth,
      sortBy
    }, result.totalCount, result.totalPages, page, 'Reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get recommended reviews for a product
 */
router.get('/recommended/:productId', (async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const limit = parseInt(req.query.limit as string) || 5;
    
    const reviews = await reviewService.getRecommendedReviews(productId, limit);
    
    res.json(ResponseData.success(reviews, 'Recommended reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get replies to a specific review
 */
router.get('/:reviewId/replies', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    // Add support for controlling max reply depth
    const maxReplyDepth = parseInt(req.query.maxReplyDepth as string) || 3;
    
    const result = await reviewService.getReviewReplies(reviewId, page, limit, maxReplyDepth);
    
    res.json(ResponseData.pageSuccess({
      reviews: result.replies, // 改为reviews字段以保持一致性
      maxReplyDepth
    }, result.totalCount, result.totalPages, page, 'Review replies retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get a specific review by ID
 */
router.get('/:reviewId', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    
    const review = await reviewService.getReviewById(reviewId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }
    
    res.json(ResponseData.success(review, 'Review retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Create a new review or reply
 */
router.post('/', (async (req: Request, res: Response) => {
  try {
    const { 
      product_id, user_id, content, rating, parent_review_id, 
      title, images, is_long_review 
    } = req.body;
    
    // Validation
    if (!product_id) {
      return res.status(400).json(ResponseData.error('Product ID is required', 400));
    }
    
    if (!user_id) {
      return res.status(400).json(ResponseData.error('User ID is required', 400));
    }
    
    if (!content || content.trim() === '') {
      return res.status(400).json(ResponseData.error('Review content is required', 400));
    }
    
    if (!parent_review_id && (!rating || rating < 1 || rating > 10)) {
      return res.status(400).json(ResponseData.error('Rating must be between 1 and 10', 400));
    }
    
    // Content length validation
    if (is_long_review && content.length > 3000) {
      return res.status(400).json(ResponseData.error('Long review content cannot exceed 3000 characters', 400));
    }
    
    if (!is_long_review && content.length > 50) {
      return res.status(400).json(ResponseData.error('Short review content cannot exceed 50 characters', 400));
    }
    
    // Title validation for long reviews
    if (is_long_review && title && (title.length < 3 || title.length > 20)) {
      return res.status(400).json(ResponseData.error('Title must be between 3 and 20 characters for long reviews', 400));
    }
    
    // Prevent title for short reviews
    if (!is_long_review && title) {
      return res.status(400).json(ResponseData.error('Short reviews cannot have a title', 400));
    }
    
    // Prevent images for short reviews
    if (!is_long_review && images && images.length > 0) {
      return res.status(400).json(ResponseData.error('Short reviews cannot have images', 400));
    }
    
    const review = await reviewService.createReview({
      product_id,
      user_id,
      content,
      rating: parent_review_id ? 1 : rating, // Replies need rating >= 1
      parent_review_id,
      title,
      images,
      is_long_review: parent_review_id ? false : is_long_review, // Replies are always short
      is_recommended: false,
      review_status: true,
      liked_by: [], // Add empty liked_by array
      disliked_by: [] // Add empty disliked_by array
    });
    
    res.status(201).json(ResponseData.success(review, 'Review created successfully', 201));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Update an existing review
 */
router.put('/:reviewId', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    const { content, rating, title, images } = req.body;
    
    // Get existing review
    const existingReview = await reviewService.getReviewById(reviewId);
    if (!existingReview) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }
    
    // Content length validation
    if (content) {
      if (existingReview.is_long_review && content.length > 3000) {
        return res.status(400).json(ResponseData.error('Long review content cannot exceed 3000 characters', 400));
      }
      
      if (!existingReview.is_long_review && content.length > 50) {
        return res.status(400).json(ResponseData.error('Short review content cannot exceed 50 characters', 400));
      }
    }
    
    // Title validation
    if (title) {
      if (existingReview.is_long_review && (title.length < 3 || title.length > 20)) {
        return res.status(400).json(ResponseData.error('Title must be between 3 and 20 characters for long reviews', 400));
      }
      
      if (!existingReview.is_long_review) {
        return res.status(400).json(ResponseData.error('Short reviews cannot have a title', 400));
      }
    }
    
    // Images validation
    if (images && !existingReview.is_long_review) {
      return res.status(400).json(ResponseData.error('Short reviews cannot have images', 400));
    }
    
    // Rating validation
    if (rating && (rating < 1 || rating > 10)) {
      return res.status(400).json(ResponseData.error('Rating must be between 1 and 10', 400));
    }
    
    // Update the review
    const updateData: any = {};
    if (content) updateData.content = content;
    if (rating && !existingReview.parent_review_id) updateData.rating = rating;
    if (existingReview.is_long_review && title) updateData.title = title;
    if (existingReview.is_long_review && images) updateData.images = images;
    
    const updatedReview = await reviewService.updateReview(reviewId, updateData);
    
    res.json(ResponseData.success(updatedReview, 'Review updated successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Delete a review
 */
router.delete('/:reviewId', (async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    
    const deletedReview = await reviewService.deleteReview(reviewId);
    if (!deletedReview) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }
    
    res.json(ResponseData.success(deletedReview, 'Review deleted successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Like a review
 */
router.post('/:reviewId/like', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await reviewService.likeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review liked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Unlike a review
 */
router.post('/:reviewId/unlike', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await reviewService.unlikeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review unliked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Dislike a review
 */
router.post('/:reviewId/dislike', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await reviewService.dislikeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review disliked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Undislike a review
 */
router.post('/:reviewId/undislike', auth, (async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user!.id; // Get user ID from authenticated request

    const review = await reviewService.undislikeReview(reviewId, userId);
    if (!review) {
      return res.status(404).json(ResponseData.error('Review not found', 404));
    }

    res.json(ResponseData.success({
      review_id: review.review_id,
      likes: review.likes,
      dislikes: review.dislikes,
      liked_by: review.liked_by,
      disliked_by: review.disliked_by
    }, 'Review undisliked successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get user reaction status for a review
 */
router.get('/:reviewId/reaction/:userId', (async (req: Request, res: Response) => {
  try {
    const { reviewId, userId } = req.params;

    const reactionStatus = await reviewService.getUserReactionStatus(reviewId, userId);

    res.json(ResponseData.success(reactionStatus, 'User reaction status retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Check if user has already reviewed a product
 */
router.get('/product/:productId/user/:userId/has-reviewed', (async (req: Request, res: Response) => {
  try {
    const { productId, userId } = req.params;

    const hasReviewed = await reviewService.hasUserReviewedProduct(productId, userId);

    res.json(ResponseData.success({ hasReviewed }, 'User review status retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get user's review for a specific product
 */
router.get('/product/:productId/user/:userId/review', (async (req: Request, res: Response) => {
  try {
    const { productId, userId } = req.params;

    const review = await reviewService.getUserReviewForProduct(productId, userId);

    if (review) {
      res.json(ResponseData.success(review, 'User review retrieved successfully', 200));
    } else {
      res.json(ResponseData.success(null, 'User has not reviewed this product', 200));
    }
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get all reviews by a specific user
 */
router.get('/user/:userId/reviews', (async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await reviewService.getUserReviews(userId, page, limit);

    res.json(ResponseData.pageSuccess({
      reviews: result.reviews
    }, result.totalCount, result.totalPages, page, 'User reviews retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get rating distribution for a product
 */
router.get('/product/:productId/rating-distribution', (async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    const distribution = await reviewService.getRatingDistribution(productId);
    const percentages = await reviewService.getRatingDistributionPercentages(productId);

    res.json(ResponseData.success({
      distribution,
      percentages
    }, 'Rating distribution retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get top-rated products
 */
router.get('/top-rated/products', (async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    
    const topRated = await productReviewSummaryService.getTopRatedProducts(limit);
    
    res.json(ResponseData.success(topRated, 'Top rated products retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

/**
 * Get most-reviewed products
 */
router.get('/most-reviewed/products', (async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 10;
    
    const mostReviewed = await productReviewSummaryService.getMostReviewedProducts(limit);
    
    res.json(ResponseData.success(mostReviewed, 'Most reviewed products retrieved successfully', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);


/**
 * Initialize sample review data for testing
 */
router.get('/init-sample-data', (async (req: Request, res: Response) => {
  try {
    let productIds = ["e1342dbe-f137-4f37-a1f0-68e95e490817","a4f73c86-93a1-4a21-ae60-7117a30d1988"];
    
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      // If no productIds provided, try to get some from the database
      const allProducts = await productService.getAllProducts();
      if (allProducts.length === 0) {
        return res.status(400).json(ResponseData.error('No products found in the database', 400));
      }
      // Use up to 5 products
      productIds = allProducts.slice(0, 5).map(p => p._id);
    }
    
    // Define the type for results array
    interface ResultItem {
      productId: string;
      status: string;
      message?: string;
      data?: {
        summary: ProductReviewSummaryData | null;
        reviews: ReviewData[];
      };
    }
    
    const results: ResultItem[] = [];
    
    for (const productId of productIds) {
      try {
        // Check if product exists
        const product = await productService.getProductById(productId);
        if (!product) {
          results.push({ productId, status: 'skipped', message: 'Product not found' });
          continue;
        }
        
        // Create sample reviews for this product
        const sampleData = await createSampleReviewsForProduct(productId);
        results.push({ productId, status: 'success', data: sampleData });
      } catch (error) {
        results.push({ 
          productId, 
          status: 'error', 
          message: `Error creating reviews: ${error.message}` 
        });
      }
    }
    
    res.status(200).json(ResponseData.success(results, 'Sample review data initialization completed', 200));
  } catch (error) {
    res.status(500).json(ResponseData.error(error.message, 500));
  }
}) as RequestHandler);

// Helper function to create sample reviews for a product
async function createSampleReviewsForProduct(productId: string): Promise<{
  summary: ProductReviewSummaryData | null;
  reviews: ReviewData[];
}> {
  // Create or update review summary
  let summary = await productReviewSummaryService.getReviewSummaryByProductId(productId);
  if (!summary) {
    summary = await productReviewSummaryService.createInitialReviewSummary(productId);
  }
  
  // Sample user IDs (for demo purposes)
  const userIds = [
    "user123", "user456", "user789", "user101", "user202",
    "user303", "user404", "user505", "user606", "user707"
  ];
  
  // Sample reviews data - ensure short reviews are ≤ 50 characters
  const sampleReviews = [
    // Long reviews
    {
      user_id: userIds[0],
      content: "This product exceeded my expectations in every way. The quality is outstanding, and my pet absolutely loves it. I've noticed a significant improvement in their energy levels and overall health since switching to this product. I would highly recommend it to any pet owner looking for premium nutrition.",
      rating: 9,
      title: "Exceptional Quality",
      images: ["image1.jpg", "image2.jpg"],
      is_long_review: true
    },
    {
      user_id: userIds[1],
      content: "While the product has many positive aspects, I found the price to be slightly higher than I would expect. However, the ingredients are clearly high quality, and my pet seems to enjoy it. I've been using it for about a month now, and have seen some improvements in my pet's coat. I'm giving it a cautious recommendation.",
      rating: 7,
      title: "Good but Pricey",
      images: ["image3.jpg"],
      is_long_review: true
    },
    {
      user_id: userIds[2],
      content: "I cannot say enough good things about this product. The ingredients are all natural and sustainably sourced, which is very important to me. My pet has been thriving since we made the switch. Their coat is shinier, they have more energy, and they seem generally happier. The price point is justified by the quality.",
      rating: 10,
      title: "Worth Every Penny",
      images: [],
      is_long_review: true
    },
    
    // Short reviews - ensuring all are ≤ 50 characters
    {
      user_id: userIds[3],
      content: "Great product, my pet loves it!",
      rating: 8,
      is_long_review: false
    },
    {
      user_id: userIds[4],
      content: "Not impressed, won't buy again.",
      rating: 3,
      is_long_review: false
    },
    {
      user_id: userIds[5],
      content: "Average quality, good value.",
      rating: 6,
      is_long_review: false
    },
    {
      user_id: userIds[6],
      content: "Excellent ingredients!",
      rating: 9,
      is_long_review: false
    },
    {
      user_id: userIds[7],
      content: "My pet refused to eat it.",
      rating: 2,
      is_long_review: false
    }
  ];
  
  const createdReviews: ReviewData[] = [];
  
  // Create the reviews
  for (const reviewData of sampleReviews) {
    try {
      const review = await reviewService.createReview({
        product_id: productId,
        ...reviewData,
        is_recommended: reviewData.rating >= 8,
        review_status: true,
        parent_review_id: undefined,
        liked_by: [],     // Initialize with empty arrays
        disliked_by: []
      });
      
      createdReviews.push(review);
    } catch (error) {
      console.error(`Error creating review: ${error.message}`);
      // Continue with other reviews if one fails
    }
  }
  
  // Add some replies to the first long review
  if (createdReviews.length > 0) {
    const parentReview = createdReviews[0];
    
    const replies = [
      {
        user_id: userIds[8],
        content: "I agree! Had the same experience.",  // Shortened to be safe
        product_id: productId,
        parent_review_id: parentReview.review_id,
        is_long_review: false,
        review_status: true,
        is_recommended: false,
        rating: 1,  // Changed from 0 to 1 to meet validation requirements
        liked_by: [], // Add empty liked_by array
        disliked_by: [] // Add empty disliked_by array
      },
      {
        user_id: userIds[9],
        content: "How long have you been using this?", // Shortened to be safe
        product_id: productId,
        parent_review_id: parentReview.review_id,
        is_long_review: false,
        review_status: true,
        is_recommended: false,
        rating: 1,  // Changed from 0 to 1 to meet validation requirements
        liked_by: [], // Add empty liked_by array
        disliked_by: [] // Add empty disliked_by array
      }
    ];
    
    for (const replyData of replies) {
      try {
        const reply = await reviewService.createReview(replyData);
        createdReviews.push(reply);
      } catch (error) {
        console.error(`Error creating reply: ${error.message}`);
        // Continue with other replies if one fails
      }
    }
  }
  
  // Add some likes to reviews - use the new method with user IDs
  if (createdReviews.length > 0) {
    try {
      // First review gets 3 likes from different users
      await reviewService.likeReview(createdReviews[0].review_id, "user505");
      await reviewService.likeReview(createdReviews[0].review_id, "user606");
      await reviewService.likeReview(createdReviews[0].review_id, "user707");
      
      // Third review gets 2 likes and 1 dislike
      if (createdReviews.length > 2) {
        await reviewService.likeReview(createdReviews[2].review_id, "user101");
        await reviewService.likeReview(createdReviews[2].review_id, "user202");
        await reviewService.dislikeReview(createdReviews[2].review_id, "user303");
      }
    } catch (error) {
      console.error(`Error adding reactions: ${error.message}`);
    }
  }
  
  // Update the summary data
  summary = await productReviewSummaryService.getReviewSummaryByProductId(productId);
  
  return {
    summary,
    reviews: createdReviews
  };
}



export default router;
