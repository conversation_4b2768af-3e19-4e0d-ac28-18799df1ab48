"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DealRepo = void 0;
const deal_1 = __importDefault(require("../models/deal"));
const base_repo_1 = require("./base-repo");
class DealRepo extends base_repo_1.BaseRepo {
    constructor() {
        super(deal_1.default);
    }
    async getDealsByProduct(productId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [deals, totalCount] = await Promise.all([
            this.model.find({
                product_id: productId,
                status: 'active'
            })
                .sort({ created_at: -1 })
                .skip(skip)
                .limit(limit)
                .lean(),
            this.model.countDocuments({
                product_id: productId,
                status: 'active'
            })
        ]);
        const totalPages = Math.ceil(totalCount / limit);
        return {
            deals: deals,
            totalCount,
            totalPages
        };
    }
    async getDealsByUser(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [deals, totalCount] = await Promise.all([
            this.model.find({
                user_id: userId,
                status: { $ne: 'deleted' }
            })
                .sort({ created_at: -1 })
                .skip(skip)
                .limit(limit)
                .lean(),
            this.model.countDocuments({
                user_id: userId,
                status: { $ne: 'deleted' }
            })
        ]);
        const totalPages = Math.ceil(totalCount / limit);
        return {
            deals: deals,
            totalCount,
            totalPages
        };
    }
    async getRecentDeals(page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [deals, totalCount] = await Promise.all([
            this.model.find({
                status: 'active'
            })
                .sort({ created_at: -1 })
                .skip(skip)
                .limit(limit)
                .lean(),
            this.model.countDocuments({
                status: 'active'
            })
        ]);
        const totalPages = Math.ceil(totalCount / limit);
        return {
            deals: deals,
            totalCount,
            totalPages
        };
    }
    async searchDeals(options) {
        const { product_id, user_id, status = 'active', deal_type, page = 1, limit = 10, sort_by = 'created_at', sort_order = 'desc' } = options;
        const skip = (page - 1) * limit;
        const query = { status };
        if (product_id)
            query.product_id = product_id;
        if (user_id)
            query.user_id = user_id;
        if (deal_type)
            query.deal_type = deal_type;
        const sortOptions = {};
        sortOptions[sort_by] = sort_order === 'asc' ? 1 : -1;
        const [deals, totalCount] = await Promise.all([
            this.model.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(limit)
                .lean(),
            this.model.countDocuments(query)
        ]);
        const totalPages = Math.ceil(totalCount / limit);
        return {
            deals: deals,
            totalCount,
            totalPages
        };
    }
    async incrementViews(dealId) {
        await this.model.findByIdAndUpdate(dealId, { $inc: { views_count: 1 } }, { new: true });
    }
    async incrementLikes(dealId) {
        await this.model.findByIdAndUpdate(dealId, { $inc: { likes_count: 1 } }, { new: true });
    }
    async decrementLikes(dealId) {
        await this.model.findByIdAndUpdate(dealId, { $inc: { likes_count: -1 } }, { new: true });
    }
    async updateDealStatus(dealId, status) {
        const deal = await this.model.findByIdAndUpdate(dealId, { status, updated_at: new Date() }, { new: true }).lean();
        return deal;
    }
}
exports.DealRepo = DealRepo;
exports.default = new DealRepo();
//# sourceMappingURL=deal-repo.js.map